# RouteMap.jsx Documentation

## نظرة عامة (Overview)

ملف `RouteMap.jsx` هو مكون React يعرض خريطة Google Maps مع إمكانية رسم مسار بين نقطتين باستخدام خدمة OpenRouteService. المكون يعرض مسار من القاهرة إلى نقطة أخرى في مصر مع markers للنقطة البداية والنهاية.

## المكونات الرئيسية (Main Components)

### 1. Route Component

```jsx
function Route()
```

**الوصف**: مكون فرعي مسؤول عن جلب بيانات المسار من OpenRouteService API ورسمه على الخريطة.

**الخصائص الرئيسية**:
- يستخدم `useMap()` hook للحصول على instance الخريطة
- يستخدم `useRef` لتخزين مرجع للـ Polyline
- يستخدم `useEffect` لتنفيذ العمليات عند تحميل المكون

### 2. RouteMap Component (Default Export)

```jsx
export default function RouteMap()
```

**الوصف**: المكون الرئيسي الذي يحتوي على الخريطة ويوفر السياق اللازم لعرضها.

## التبعيات (Dependencies)

```jsx
import React, { useEffect, useRef } from "react";
import {
  APIProvider,
  Map,
  useMap,
} from "@vis.gl/react-google-maps";
```

- **React**: للـ hooks والمكونات
- **@vis.gl/react-google-maps**: مكتبة React wrapper لـ Google Maps

## متغيرات البيئة (Environment Variables)

```javascript
process.env.REACT_APP_GOOGLE_MAPS_API_KEY  // مفتاح Google Maps API
process.env.REACT_APP_ORS_KEY              // مفتاح OpenRouteService API
```

## تفاصيل التنفيذ (Implementation Details)

### جلب بيانات المسار

```javascript
const fetchRoute = async () => {
  const apiKey = process.env.REACT_APP_ORS_KEY;
  const start = '31.2357,30.0444'; // lon,lat للقاهرة
  const end = '31.1313,30.0131';   // النقطة النهائية
  const url = `https://api.openrouteservice.org/v2/directions/driving-car?api_key=${apiKey}&start=${start}&end=${end}`;
}
```

**ملاحظات مهمة**:
- OpenRouteService يتوقع الإحداثيات بصيغة `longitude,latitude`
- يتم استخدام نوع المسار `driving-car`
- يتم التعامل مع الأخطاء والإلغاء باستخدام AbortController

### رسم المسار

```javascript
const poly = new window.google.maps.Polyline({
  path,                    // مصفوفة النقاط
  geodesic: true,         // خط منحني يتبع شكل الأرض
  strokeColor: '#00FF00', // لون أخضر
  strokeOpacity: 1,       // شفافية كاملة
  strokeWeight: 4,        // سمك الخط
});
```

### إضافة Markers

```javascript
new window.google.maps.Marker({ position: path[0], map });              // نقطة البداية
new window.google.maps.Marker({ position: path[path.length-1], map });  // نقطة النهاية
```

### تعديل عرض الخريطة

```javascript
const bounds = new window.google.maps.LatLngBounds();
path.forEach(p => bounds.extend(p));
map.fitBounds(bounds);
```

يتم تعديل zoom والموقع ليشمل كامل المسار.

## إعدادات الخريطة (Map Configuration)

```jsx
<Map
  style={{ width: "100%", height: "100vh" }}  // ملء الشاشة
  defaultCenter={{ lat: 30.0444, lng: 31.2357 }} // مركز القاهرة
  defaultZoom={6}                               // مستوى التكبير
  gestureHandling="greedy"                      // التحكم بالإيماءات
  disableDefaultUI={false}                      // عرض أدوات التحكم
>
```

## إدارة الذاكرة (Memory Management)

```javascript
return () => {
  controller.abort();           // إلغاء طلبات API المعلقة
  if (routeRef.current) {
    routeRef.current.setMap(null); // إزالة Polyline من الخريطة
    routeRef.current = null;       // تنظيف المرجع
  }
};
```

يتم تنظيف الموارد عند إلغاء تحميل المكون لتجنب memory leaks.

## معالجة الأخطاء (Error Handling)

```javascript
try {
  // جلب البيانات
} catch (err) {
  if (err.name === 'AbortError') return; // تجاهل أخطاء الإلغاء
  console.error('Route error:', err);    // طباعة الأخطاء الأخرى
}
```

## الاستخدام (Usage)

```jsx
import RouteMap from './RouteMap';

function App() {
  return <RouteMap />;
}
```

## المتطلبات (Requirements)

1. **Google Maps API Key**: مطلوب للخريطة الأساسية
2. **OpenRouteService API Key**: مطلوب لحساب المسارات
3. **اتصال إنترنت**: لجلب بيانات الخريطة والمسارات

## الأمان (Security)

- تأكد من حماية API keys وعدم تعريضها في الكود
- استخدم متغيرات البيئة لتخزين المفاتيح
- قم بتقييد استخدام API keys للدومين المحدد
