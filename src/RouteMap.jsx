import React, { useEffect,useRef } from "react";
import {
  APIProvider,
  Map,
  useMap,
} from "@vis.gl/react-google-maps";

// ✅ Route component لرسم خط أخضر
// ✅ Route component باستخدام OpenRouteService (مجاني)
function Route() {
  const map = useMap();
  const routeRef = useRef(null);

  useEffect(() => {
    if (!map) return;
    const controller = new AbortController();

    const fetchRoute = async () => {
      try {
        const apiKey = process.env.REACT_APP_ORS_KEY;
        // IMPORTANT: OpenRouteService expects start=lon,lat and end=lon,lat
        const start = '31.2357,30.0444'; // lon,lat for Cairo
        const end = '31.1313,30.0131';
        const url = `https://api.openrouteservice.org/v2/directions/driving-car?api_key=${apiKey}&start=${start}&end=${end}`;

        const res = await fetch(url, { signal: controller.signal });
        if (!res.ok) throw new Error(`HTTP ${res.status}`);

        const data = await res.json();
        const coords = data?.features?.[0]?.geometry?.coordinates;
        if (!coords || coords.length === 0) throw new Error('No route coordinates');

        const path = coords.map(c => ({ lat: c[1], lng: c[0] }));

        const poly = new window.google.maps.Polyline({
          path,
          geodesic: true,
          strokeColor: '#00FF00',
          strokeOpacity: 1,
          strokeWeight: 4,
        });

        poly.setMap(map);
        routeRef.current = poly;

        // Fit map to route bounds
        const bounds = new window.google.maps.LatLngBounds();
        path.forEach(p => bounds.extend(p));
        map.fitBounds(bounds);

        // Optional: add start/end markers (and store refs if you want to remove them later)
        new window.google.maps.Marker({ position: path[0], map });
        new window.google.maps.Marker({ position: path[path.length-1], map });

      } catch (err) {
        if (err.name === 'AbortError') return;
        console.error('Route error:', err);
      }
    };

    fetchRoute();

    return () => {
      controller.abort();
      if (routeRef.current) {
        routeRef.current.setMap(null);
        routeRef.current = null;
      }
    };
  }, [map]);

  return null;
}

export default function ClusterMap() {
  return (
    <APIProvider apiKey={process.env.REACT_APP_GOOGLE_MAPS_API_KEY}>
      <Map
        style={{ width: "100%", height: "100vh" }}
        defaultCenter={{ lat: 30.0444, lng: 31.2357 }}
        defaultZoom={6}
        gestureHandling="greedy"
        disableDefaultUI={false}
      >
        <Route />
      </Map>
    </APIProvider>
  );
}
