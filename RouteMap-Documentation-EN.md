# RouteMap.jsx Documentation

## Overview

The `RouteMap.jsx` file is a React component that displays a Google Maps interface with route rendering capabilities between two points using the OpenRouteService API. The component shows a route from Cairo to another point in Egypt with markers for start and end locations.

## Main Components

### 1. Route Component

```jsx
function Route()
```

**Description**: A child component responsible for fetching route data from the OpenRouteService API and rendering it on the map.

**Key Features**:
- Uses `useMap()` hook to access the map instance
- Uses `useRef` to store a reference to the Polyline
- Uses `useEffect` to execute operations when the component mounts

### 2. RouteMap Component (Default Export)

```jsx
export default function RouteMap()
```

**Description**: The main component that contains the map and provides the necessary context for rendering.

## Dependencies

```jsx
import React, { useEffect, useRef } from "react";
import {
  APIProvider,
  Map,
  useMap,
} from "@vis.gl/react-google-maps";
```

- **React**: For hooks and components
- **@vis.gl/react-google-maps**: React wrapper library for Google Maps

## Environment Variables

```javascript
process.env.REACT_APP_GOOGLE_MAPS_API_KEY  // Google Maps API key
process.env.REACT_APP_ORS_KEY              // OpenRouteService API key
```

## Implementation Details

### Route Data Fetching

```javascript
const fetchRoute = async () => {
  const apiKey = process.env.REACT_APP_ORS_KEY;
  const start = '31.2357,30.0444'; // lon,lat for Cairo
  const end = '31.1313,30.0131';   // End point coordinates
  const url = `https://api.openrouteservice.org/v2/directions/driving-car?api_key=${apiKey}&start=${start}&end=${end}`;
}
```

**Important Notes**:
- OpenRouteService expects coordinates in `longitude,latitude` format
- Uses `driving-car` route type
- Handles errors and cancellation using AbortController

### Route Rendering

```javascript
const poly = new window.google.maps.Polyline({
  path,                    // Array of coordinates
  geodesic: true,         // Curved line following Earth's shape
  strokeColor: '#00FF00', // Green color
  strokeOpacity: 1,       // Full opacity
  strokeWeight: 4,        // Line thickness
});
```

### Adding Markers

```javascript
new window.google.maps.Marker({ position: path[0], map });              // Start point
new window.google.maps.Marker({ position: path[path.length-1], map });  // End point
```

### Map View Adjustment

```javascript
const bounds = new window.google.maps.LatLngBounds();
path.forEach(p => bounds.extend(p));
map.fitBounds(bounds);
```

Adjusts zoom and position to include the entire route.

## Map Configuration

```jsx
<Map
  style={{ width: "100%", height: "100vh" }}  // Full screen
  defaultCenter={{ lat: 30.0444, lng: 31.2357 }} // Cairo center
  defaultZoom={6}                               // Zoom level
  gestureHandling="greedy"                      // Gesture control
  disableDefaultUI={false}                      // Show control UI
>
```

## Memory Management

```javascript
return () => {
  controller.abort();           // Cancel pending API requests
  if (routeRef.current) {
    routeRef.current.setMap(null); // Remove Polyline from map
    routeRef.current = null;       // Clean up reference
  }
};
```

Resources are cleaned up when the component unmounts to prevent memory leaks.

## Error Handling

```javascript
try {
  // Fetch data
} catch (err) {
  if (err.name === 'AbortError') return; // Ignore cancellation errors
  console.error('Route error:', err);    // Log other errors
}
```

## Usage

```jsx
import RouteMap from './RouteMap';

function App() {
  return <RouteMap />;
}
```

## Requirements

1. **Google Maps API Key**: Required for the base map
2. **OpenRouteService API Key**: Required for route calculations
3. **Internet Connection**: For fetching map and route data

## API Integration

### OpenRouteService API

- **Endpoint**: `https://api.openrouteservice.org/v2/directions/driving-car`
- **Method**: GET
- **Parameters**:
  - `api_key`: Your OpenRouteService API key
  - `start`: Starting coordinates (longitude,latitude)
  - `end`: Ending coordinates (longitude,latitude)

### Response Format

```json
{
  "features": [
    {
      "geometry": {
        "coordinates": [[lng, lat], [lng, lat], ...]
      }
    }
  ]
}
```

## Component Lifecycle

1. **Mount**: Component initializes and map loads
2. **Effect**: `useEffect` triggers route fetching
3. **API Call**: Fetches route data from OpenRouteService
4. **Rendering**: Creates Polyline and markers on map
5. **Cleanup**: Removes elements when component unmounts

## Performance Considerations

- **AbortController**: Prevents memory leaks from cancelled requests
- **Ref Management**: Proper cleanup of map elements
- **Single Route**: Only one route is displayed at a time
- **Bounds Fitting**: Automatically adjusts view to show entire route

## Security

- Protect API keys and avoid exposing them in code
- Use environment variables to store keys
- Restrict API key usage to specific domains
- Implement rate limiting for API calls

## Troubleshooting

### Common Issues

1. **Map not loading**: Check Google Maps API key
2. **Route not appearing**: Verify OpenRouteService API key
3. **CORS errors**: Ensure proper API configuration
4. **Performance issues**: Check for memory leaks in cleanup

### Debug Tips

```javascript
// Add logging for debugging
console.log('Route data:', data);
console.log('Processed path:', path);
console.log('Map instance:', map);
```

## Browser Compatibility

- **Modern Browsers**: Chrome, Firefox, Safari, Edge
- **Mobile Support**: Responsive design works on mobile devices
- **JavaScript**: Requires ES6+ support

## File Structure

```
src/
├── RouteMap.jsx           # Main component file
├── RouteMap-Documentation.md     # Arabic documentation
└── RouteMap-Documentation-EN.md  # English documentation
```
